{"generatedAt": "2025-07-30T17:14:44.007Z", "sdkVersion": "8.2.7", "inputHash": "a542a848118fa332b6371ba0bf61a8ec", "schemaHash": "418dfedeefadde2131d77afe73c7250b", "resolvedRef": {"repoHash": "57ec52db", "type": "branch", "ref": "main", "createSuggestedBranchLink": null, "id": "KluwvFPvKCxusUOmSQG4q", "name": "main", "git": null, "createdAt": "2025-06-16T00:30:26.760Z", "archivedAt": null, "archivedBy": null, "headCommitId": "HijQ0sLasCsht2mw0eLWG", "isDefault": true, "deletedAt": null, "workingRootBlockId": "a8Oul5Re6jsffvG4Ab5XZ"}}