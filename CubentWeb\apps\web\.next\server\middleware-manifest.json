{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/9583c_@sentry_core_build_esm_1adfbc63._.js", "server/edge/chunks/4b803_@sentry_vercel-edge_build_esm_index_de3a4916.js", "server/edge/chunks/ec4b9_zod_dist_esm_d6b871ea._.js", "server/edge/chunks/node_modules__pnpm_f5a78348._.js", "server/edge/chunks/[root-of-the-server]__a0a0371e._.js", "server/edge/chunks/apps_web_edge-wrapper_88d0945e.js", "server/edge/chunks/_c8462c85._.js", "server/edge/chunks/ec4b9_zod_dist_esm_cbcb71bd._.js", "server/edge/chunks/eec21_@clerk_shared_dist_40b2e982._.js", "server/edge/chunks/c67f4_@clerk_backend_dist_d8cc056d._.js", "server/edge/chunks/25c57_@clerk_nextjs_dist_esm_1ca17405._.js", "server/edge/chunks/node_modules__pnpm_2d5523b0._.js", "server/edge/chunks/[root-of-the-server]__b5fdeec6._.js", "server/edge/chunks/apps_web_edge-wrapper_ada886b9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|images|ingest|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|images|ingest|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "9uQifFpW3FDh5bZlRviSaCJshVr+8Qe5JU48C94JGDQ=", "__NEXT_PREVIEW_MODE_ID": "5395d261a60c9863f7686d613fc0f95c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0a5807a1d4d0f99636e1df223b5475c875e52e9c675e629b2e88b38fe3705ad0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "65ea35eaa450d4fbbb253b32082af472a9daa6618fcb9c79ba1d56bce088522c"}}}, "sortedMiddleware": ["/"], "functions": {}}