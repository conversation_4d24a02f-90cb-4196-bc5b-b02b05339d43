{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/%28home%29/components/animated-title.tsx"], "sourcesContent": ["export const AnimatedTitle = () => {\n  return (\n    <h1 className=\"max-w-4xl text-center font-regular text-[48px] tracking-tighter md:text-6xl relative z-10 leading-tight\">\n      Built to optimize. Trained to ship.\n    </h1>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAO,MAAM,gBAAgB;IAC3B,qBACE,6VAAC;QAAG,WAAU;kBAA0G;;;;;;AAI5H", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/%28home%29/components/trusted-by.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TrustedBy = registerClientReference(\n    function() { throw new Error(\"Attempted to call TrustedBy() from the server but TrustedBy is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/trusted-by.tsx <module evaluation>\",\n    \"TrustedBy\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,wFACA", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/%28home%29/components/trusted-by.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TrustedBy = registerClientReference(\n    function() { throw new Error(\"Attempted to call TrustedBy() from the server but TrustedBy is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/trusted-by.tsx\",\n    \"TrustedBy\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oEACA", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/%28home%29/components/hero.tsx"], "sourcesContent": ["import { env } from '@/env';\nimport { blog } from '@repo/cms';\nimport { Feed } from '@repo/cms/components/feed';\nimport { Button } from '@repo/design-system/components/ui/button';\nimport type { Dictionary } from '@repo/internationalization';\nimport { ExternalLink, MoveRight, PhoneCall, Lock } from 'lucide-react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { AnimatedTitle } from './animated-title';\nimport { TrustedBy } from './trusted-by';\n\ntype HeroProps = {\n  dictionary: Dictionary;\n};\n\n// Hero component for the homepage - updated\nexport const Hero = async ({ dictionary }: HeroProps) => (\n  <div className=\"w-full relative overflow-hidden\">\n\n\n\n\n\n\n\n\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n      <div className=\"flex flex-col items-center justify-center gap-8 pt-6 pb-8 lg:pt-12 lg:pb-12\">\n        <div>\n          <Feed queries={[blog.latestPostQuery]}>\n            {/* biome-ignore lint/suspicious/useAwait: \"Server Actions must be async\" */}\n            {async ([data]: [any]) => {\n              'use server';\n\n              return (\n                <Button variant=\"secondary\" size=\"sm\" className=\"gap-4 bg-gradient-to-r from-white/10 to-white/10 border-white/20 hover:from-white/20 hover:to-white/20 hover:border-white/30 text-white hover:text-white/90\" asChild>\n                  <Link href={`/blog/${data.blog.posts.item?._slug}`}>\n                    {dictionary.web.home.hero.announcement}{' '}\n                    <MoveRight className=\"h-4 w-4 text-white\" />\n                  </Link>\n                </Button>\n              );\n            }}\n          </Feed>\n        </div>\n        <div className=\"flex flex-col gap-6 relative\">\n          <div className=\"flex flex-col gap-6 relative\">\n            <AnimatedTitle />\n            <p className=\"max-w-3xl mx-auto text-center text-lg text-muted-foreground leading-relaxed tracking-tight md:text-xl relative z-10\">\n              Meet Cubent.Dev, your AI coding partner inside the editor. Generate code, solve bugs, document faster, and build smarter with simple language.\n            </p>\n          </div>\n          <div className=\"flex flex-col sm:flex-row gap-4 mt-8 justify-center\">\n          <Button\n            variant=\"outline\"\n            className=\"px-4 py-5 text-lg font-medium sm:px-4 sm:py-5 sm:text-lg transition-all duration-200 group hover:!bg-[#2a2a2a] hover:!border-[#2a2a2a]\"\n            style={{ backgroundColor: '#faf9f6', borderColor: '#faf9f6', color: '#2a2a2a' }}\n            asChild\n          >\n            <Link\n              href=\"https://marketplace.visualstudio.com/items?itemName=Cubent.cubent\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"transition-all duration-200\"\n            >\n              <div className=\"flex items-center gap-2 sm:gap-2\">\n                <div className=\"flex items-center justify-center w-8 h-8 rounded transition-all duration-200 group-hover:bg-[rgba(250,249,246,0.1)]\" style={{ backgroundColor: 'rgba(42, 42, 42, 0.1)' }}>\n                  <svg className=\"w-5 h-5 sm:w-5 sm:h-5 transition-all duration-200 group-hover:!text-[#faf9f6]\" style={{ color: '#2a2a2a' }} viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                    <path d=\"M23.15 2.587L18.21.21a1.494 1.494 0 0 0-1.705.29l-9.46 8.63-4.12-3.128a.999.999 0 0 0-1.276.057L.327 7.261A1 1 0 0 0 .326 8.74L3.899 12 .326 15.26a1 1 0 0 0 .001 1.479L1.65 17.94a.999.999 0 0 0 1.276.057l4.12-3.128 9.46 8.63a1.492 1.492 0 0 0 1.704.29l4.942-2.377A1.5 1.5 0 0 0 24 20.06V3.939a1.5 1.5 0 0 0-.85-1.352zm-5.146 14.861L10.826 12l7.178-5.448v10.896z\"/>\n                  </svg>\n                </div>\n                <span className=\"transition-all duration-200 group-hover:!text-[#faf9f6]\" style={{ color: '#2a2a2a' }}>Install in VS Code</span>\n              </div>\n            </Link>\n          </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    {/* Company logos section moved above GIF */}\n    <TrustedBy dictionary={dictionary} />\n\n\n\n\n\n\n\n\n\n\n\n\n  </div>\n);\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAAA;AACA;AAEA;AACA;AAEA;AACA;;;;;;;;;;;MAsBa,wEAAO,CAAC,KAAY;;IAGnB,qBACE,6VAAC,2JAAA,CAAA,SAAM;QAAC,SAAQ;QAAY,MAAK;QAAK,WAAU;QAA8J,OAAO;kBACnN,cAAA,6VAAC,2QAAA,CAAA,UAAI;YAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO;;;gBACR;8BACxC,6VAAC,oSAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B;AA1BL,MAAM,OAAO,OAAO,EAAE,UAAU,EAAa,iBAClD,6VAAC;QAAI,WAAU;;0BASb,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;sCACC,cAAA,6VAAC,sLAAA,CAAA,OAAI;gCAAC,SAAS;oCAAC,wHAAA,CAAA,OAAI,CAAC,eAAe;iCAAC;0CAElC,8VAAA;;;;;;;;;;;sCAcL,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,gLAAA,CAAA,gBAAa;;;;;sDACd,6VAAC;4CAAE,WAAU;sDAAsH;;;;;;;;;;;;8CAIrI,6VAAC;oCAAI,WAAU;8CACf,cAAA,6VAAC,2JAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,OAAO;4CAAE,iBAAiB;4CAAW,aAAa;4CAAW,OAAO;wCAAU;wCAC9E,OAAO;kDAEP,cAAA,6VAAC,2QAAA,CAAA,UAAI;4CACH,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAI,WAAU;wDAAsH,OAAO;4DAAE,iBAAiB;wDAAwB;kEACrL,cAAA,6VAAC;4DAAI,WAAU;4DAAgF,OAAO;gEAAE,OAAO;4DAAU;4DAAG,SAAQ;4DAAY,MAAK;sEACnJ,cAAA,6VAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;kEAGZ,6VAAC;wDAAK,WAAU;wDAA0D,OAAO;4DAAE,OAAO;wDAAU;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnH,6VAAC,4KAAA,CAAA,YAAS;gBAAC,YAAY", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/analytics/posthog/server.ts"], "sourcesContent": ["import 'server-only';\nimport { PostHog } from 'posthog-node';\nimport { keys } from '../keys';\n\nconst envKeys = keys();\n\nexport const analytics = envKeys.NEXT_PUBLIC_POSTHOG_KEY && envKeys.NEXT_PUBLIC_POSTHOG_HOST\n  ? new PostHog(envKeys.NEXT_PUBLIC_POSTHOG_KEY, {\n      host: envKeys.NEXT_PUBLIC_POSTHOG_HOST,\n\n      // Don't batch events and flush immediately - we're running in a serverless environment\n      flushAt: 1,\n      flushInterval: 0,\n    })\n  : null;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,UAAU,CAAA,GAAA,6HAAA,CAAA,OAAI,AAAD;AAEZ,MAAM,YAAY,QAAQ,uBAAuB,IAAI,QAAQ,wBAAwB,GACxF,IAAI,qNAAA,CAAA,UAAO,CAAC,QAAQ,uBAAuB,EAAE;IAC3C,MAAM,QAAQ,wBAAwB;IAEtC,uFAAuF;IACvF,SAAS;IACT,eAAe;AACjB,KACA", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/auth/server.ts"], "sourcesContent": ["import 'server-only';\n\nexport * from '@clerk/nextjs/server';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/feature-flags/lib/create-flag.ts"], "sourcesContent": ["import { analytics } from '@repo/analytics/posthog/server';\nimport { auth } from '@repo/auth/server';\nimport { flag } from 'flags/next';\n\nexport const createFlag = (key: string) =>\n  flag({\n    key,\n    defaultValue: false,\n    async decide() {\n      const { userId } = await auth();\n\n      if (!userId) {\n        return this.defaultValue as boolean;\n      }\n\n      const isEnabled = analytics\n        ? await analytics.isFeatureEnabled(key, userId)\n        : null;\n\n      return isEnabled ?? (this.defaultValue as boolean);\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEO,MAAM,aAAa,CAAC,MACzB,CAAA,GAAA,8OAAA,CAAA,OAAI,AAAD,EAAE;QACH;QACA,cAAc;QACd,MAAM;YACJ,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,6RAAA,CAAA,OAAI,AAAD;YAE5B,IAAI,CAAC,QAAQ;gBACX,OAAO,IAAI,CAAC,YAAY;YAC1B;YAEA,MAAM,YAAY,0IAAA,CAAA,YAAS,GACvB,MAAM,0IAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,KAAK,UACtC;YAEJ,OAAO,aAAc,IAAI,CAAC,YAAY;QACxC;IACF", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/packages/feature-flags/index.ts"], "sourcesContent": ["import { createFlag } from './lib/create-flag';\n\nexport const showBetaFeature = createFlag('showBetaFeature');\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/%28home%29/components/community.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Community = registerClientReference(\n    function() { throw new Error(\"Attempted to call Community() from the server but Community is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/community.tsx <module evaluation>\",\n    \"Community\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,uFACA", "debugId": null}}, {"offset": {"line": 484, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/%28home%29/components/community.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Community = registerClientReference(\n    function() { throw new Error(\"Attempted to call Community() from the server but Community is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/community.tsx\",\n    \"Community\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,mEACA", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/%28home%29/components/download.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Download = registerClientReference(\n    function() { throw new Error(\"Attempted to call Download() from the server but Download is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/download.tsx <module evaluation>\",\n    \"Download\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,sFACA", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/%28home%29/components/download.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Download = registerClientReference(\n    function() { throw new Error(\"Attempted to call Download() from the server but Download is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/download.tsx\",\n    \"Download\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,kEACA", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 546, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/%28home%29/components/mockup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Mockup = registerClientReference(\n    function() { throw new Error(\"Attempted to call Mockup() from the server but <PERSON>ck<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/mockup.tsx <module evaluation>\",\n    \"Mockup\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/apps/web/app/[locale]/(home)/components/mockup.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/mockup.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,oFACA;uCAEW,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsT,GACnV,oFACA", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/%28home%29/components/mockup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Mockup = registerClientReference(\n    function() { throw new Error(\"Attempted to call Mockup() from the server but <PERSON>ck<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/mockup.tsx\",\n    \"Mockup\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/apps/web/app/[locale]/(home)/components/mockup.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/mockup.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,gEACA;uCAEW,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 592, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/%28home%29/components/model-providers.tsx"], "sourcesContent": ["import React from 'react';\n\nexport const ModelProviders = () => {\n  return (\n    <div className=\"w-full relative px-4 sm:px-6\" style={{ backgroundColor: '#161616' }}>\n      <div\n        className=\"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 py-24 lg:py-36 relative\"\n        style={{\n          border: '1px solid rgba(255, 255, 255, 0.08)',\n          borderTop: 'none',\n          backgroundColor: 'transparent'\n        }}\n      >\n        <div className=\"flex flex-col items-center justify-center gap-12\">\n          {/* Header */}\n          <div className=\"flex flex-col gap-4 text-center max-w-4xl mx-auto\">\n            <h2 className=\"font-regular text-3xl tracking-tighter md:text-4xl\">\n              First-class support for every major model provider\n            </h2>\n            <p className=\"max-w-2xl mx-auto text-lg text-muted-foreground leading-relaxed tracking-tight\">\n              Connect with the AI models you trust. <PERSON><PERSON><PERSON> works seamlessly with all leading providers.\n            </p>\n          </div>\n\n          {/* Model Provider Logos */}\n          <div className=\"w-full max-w-6xl mx-auto\">\n            <div className=\"flex flex-wrap items-center justify-center gap-8 md:gap-12 lg:gap-16\">\n\n              {/* OpenAI */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/openai.png\"\n                    alt=\"OpenAI\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">OpenAI</span>\n              </div>\n\n              {/* Anthropic */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/anthropic.png\"\n                    alt=\"Anthropic\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Anthropic</span>\n              </div>\n\n              {/* Google */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/gemini-color.png\"\n                    alt=\"Google Gemini\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Google</span>\n              </div>\n\n              {/* Cohere */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/cohere-color.png\"\n                    alt=\"Cohere\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Cohere</span>\n              </div>\n\n              {/* Mistral */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/mistral-color.png\"\n                    alt=\"Mistral\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">Mistral</span>\n              </div>\n\n              {/* OpenRouter */}\n              <div className=\"flex items-center gap-3 group\">\n                <div className=\"w-12 h-12 flex items-center justify-center\">\n                  <img\n                    src=\"https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/dark/openrouter.png\"\n                    alt=\"OpenRouter\"\n                    className=\"w-10 h-10 grayscale opacity-50 group-hover:opacity-80 transition-opacity duration-300\"\n                  />\n                </div>\n                <span className=\"text-lg font-medium text-muted-foreground\">OpenRouter</span>\n              </div>\n\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEO,MAAM,iBAAiB;IAC5B,qBACE,6VAAC;QAAI,WAAU;QAA+B,OAAO;YAAE,iBAAiB;QAAU;kBAChF,cAAA,6VAAC;YACC,WAAU;YACV,OAAO;gBACL,QAAQ;gBACR,WAAW;gBACX,iBAAiB;YACnB;sBAEA,cAAA,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,6VAAC;gCAAE,WAAU;0CAAiF;;;;;;;;;;;;kCAMhG,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;;8CAGb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;8CAI9D,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,6VAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5E", "debugId": null}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/%28home%29/components/speed-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SpeedSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call SpeedSection() from the server but SpeedSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx <module evaluation>\",\n    \"SpeedSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2FACA", "debugId": null}}, {"offset": {"line": 889, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/%28home%29/components/speed-section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const SpeedSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call SpeedSection() from the server but SpeedSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/speed-section.tsx\",\n    \"SpeedSection\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,uEACA", "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/CubentWeb/apps/web/app/%5Blocale%5D/%28home%29/page.tsx"], "sourcesContent": ["import { showBetaFeature } from '@repo/feature-flags';\nimport { getDictionary } from '@repo/internationalization';\nimport { createMetadata } from '@repo/seo/metadata';\nimport type { Metadata } from 'next';\nimport Image from 'next/image';\nimport { Community } from './components/community';\nimport { CTA } from './components/cta';\nimport { Download } from './components/download';\nimport { FAQ } from './components/faq';\n\nimport { Hero } from './components/hero';\nimport { Mockup } from './components/mockup';\nimport { ModelProviders } from './components/model-providers';\nimport { SpeedSection } from './components/speed-section';\nimport { Stats } from './components/stats';\nimport { Testimonials } from './components/testimonials';\nimport { TrustedBy } from './components/trusted-by';\n\n\n\ntype HomeProps = {\n  params: Promise<{\n    locale: string;\n  }>;\n};\n\nexport const generateMetadata = async ({\n  params,\n}: HomeProps): Promise<Metadata> => {\n  const { locale } = await params;\n  const dictionary = await getDictionary(locale);\n\n  // Use custom metadata for homepage to avoid \"| Cubent\" suffix\n  const homeMetadata = dictionary.web.home.meta;\n\n  return {\n    title: homeMetadata.title, // Just the title without \"| Cubent\"\n    description: homeMetadata.description,\n    applicationName: 'Cubent',\n    metadataBase: process.env.VERCEL_PROJECT_PRODUCTION_URL\n      ? new URL(`https://${process.env.VERCEL_PROJECT_PRODUCTION_URL}`)\n      : undefined,\n    authors: [{ name: 'Cubent', url: 'https://cubent.dev/' }],\n    creator: 'Cubent',\n    formatDetection: {\n      telephone: false,\n    },\n    appleWebApp: {\n      capable: true,\n      statusBarStyle: 'default',\n      title: homeMetadata.title,\n    },\n    openGraph: {\n      title: homeMetadata.title,\n      description: homeMetadata.description,\n      type: 'website',\n      siteName: 'Cubent',\n      locale: 'en_US',\n    },\n    publisher: 'Cubent',\n    twitter: {\n      card: 'summary_large_image',\n      creator: '@cubent',\n    },\n  };\n};\n\nconst Home = async ({ params }: HomeProps) => {\n  const { locale } = await params;\n  const dictionary = await getDictionary(locale);\n  const betaFeature = await showBetaFeature();\n\n  return (\n    <>\n      {betaFeature && (\n        <div className=\"w-full bg-black py-2 text-center text-white\">\n          Beta feature now available\n        </div>\n      )}\n      {/* Transparent box wrapper for Hero and TrustedBy sections */}\n      <div className=\"w-full relative px-4 sm:px-6\" style={{ backgroundColor: '#161616' }}>\n        <div\n          className=\"max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 -mt-20 pt-20 relative\"\n          style={{\n            border: '1px solid rgba(255, 255, 255, 0.08)',\n            borderTop: 'none',\n            backgroundColor: 'transparent'\n          }}\n        >\n          {/* Subtle centered white gradient */}\n          <div\n            className=\"absolute inset-0 pointer-events-none\"\n            style={{\n              background: 'radial-gradient(ellipse 800px 600px at center, rgba(255, 255, 255, 0.06) 0%, transparent 50%)'\n            }}\n          ></div>\n\n\n\n          <div className=\"relative z-10\">\n            <Hero dictionary={dictionary} />\n          </div>\n        </div>\n      </div>\n\n      {/* New section attached to hero */}\n      <div className=\"w-full px-0 md:px-4 lg:px-6\" style={{ backgroundColor: '#161616' }}>\n        <div\n          className=\"w-full md:max-w-7xl md:mx-auto px-0 md:px-6 lg:px-12 relative overflow-hidden\"\n          style={{\n            backgroundColor: '#161616',\n            border: '1px solid rgba(255, 255, 255, 0.08)',\n            borderTop: 'none'\n          }}\n        >\n\n\n            <div className=\"relative z-10 p-4 md:p-12 pt-16 md:pt-12\">\n              <div className=\"text-center mb-16 md:mb-10\">\n                <h2 className=\"text-3xl md:text-4xl text-white leading-tight\">\n                  Code faster and smarter\n                </h2>\n                <p className=\"text-xl md:text-2xl text-white/60 -mt-1\">\n                  without leaving your editor\n                </p>\n              </div>\n\n              {/* Feature tabs */}\n              <div className=\"flex justify-center gap-3 mb-6 flex-wrap md:flex-wrap\">\n                <div className=\"px-3 py-1.5 rounded-full border border-white/20 text-white/60 text-xs whitespace-nowrap\">\n                  AI Assistant\n                </div>\n                <div className=\"px-3 py-1.5 rounded-full border border-white/20 text-white/60 text-xs whitespace-nowrap\">\n                  Code Generation\n                </div>\n                <div className=\"px-3 py-1.5 rounded-full border border-white/20 text-white/60 text-xs whitespace-nowrap\">\n                  Smart Completion\n                </div>\n              </div>\n\n              {/* Feature checkmarks */}\n              <div className=\"flex justify-center gap-6 mb-10 flex-wrap md:flex-wrap text-xs\">\n                <div className=\"flex items-center gap-1.5 text-white/60 whitespace-nowrap\">\n                  <svg className=\"w-3 h-3 text-white/40\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Real-time Assistance\n                </div>\n                <div className=\"flex items-center gap-1.5 text-white/60 whitespace-nowrap\">\n                  <svg className=\"w-3 h-3 text-white/40\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Context Aware\n                </div>\n                <div className=\"flex items-center gap-1.5 text-white/60 whitespace-nowrap\">\n                  <svg className=\"w-3 h-3 text-white/40\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Multi-Language\n                </div>\n                <div className=\"flex items-center gap-1.5 text-white/60 whitespace-nowrap\">\n                  <svg className=\"w-3 h-3 text-white/40\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Instant Debugging\n                </div>\n                <div className=\"flex items-center gap-1.5 text-white/60 whitespace-nowrap\">\n                  <svg className=\"w-3 h-3 text-white/40\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Easy as typing\n                </div>\n              </div>\n            </div>\n\n            {/* Full-width GIF container */}\n            <div className=\"relative w-full mt-4 px-4 mb-8\">\n              <div className=\"relative border border-white/20 overflow-hidden bg-black/40 backdrop-blur-sm w-full\">\n                {/* Window header with dots */}\n                <div className=\"flex items-center justify-between px-4 py-3 bg-black/5 border-b border-white/5\">\n                  <div className=\"flex items-center gap-2\">\n                    <div className=\"w-3 h-3 rounded-full bg-white/20\"></div>\n                    <div className=\"w-3 h-3 rounded-full bg-white/20\"></div>\n                    <div className=\"w-3 h-3 rounded-full bg-white/20\"></div>\n                  </div>\n                </div>\n                <Image\n                  src=\"/images/Introducing-Cubent-The-Smartest-AI-Coder-4.gif\"\n                  alt=\"Cubent Dev Demo - The Smartest AI Coder\"\n                  width={1400}\n                  height={787}\n                  className=\"w-full h-auto\"\n                  style={{\n                    maxWidth: '100%'\n                  }}\n                  unoptimized={true}\n                />\n              </div>\n            </div>\n        </div>\n      </div>\n\n      <Mockup />\n\n      <ModelProviders />\n      <SpeedSection />\n      <Community dictionary={dictionary} />\n      <Download />\n    </>\n  );\n};\n\nexport default Home;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAGA;AACA;AAEA;AAGA;AACA;AACA;AACA;;;;;;;;;;;AAaO,MAAM,mBAAmB,OAAO,EACrC,MAAM,EACI;IACV,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,aAAa,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IAEvC,8DAA8D;IAC9D,MAAM,eAAe,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI;IAE7C,OAAO;QACL,OAAO,aAAa,KAAK;QACzB,aAAa,aAAa,WAAW;QACrC,iBAAiB;QACjB,cAAc,QAAQ,GAAG,CAAC,6BAA6B,GACnD,IAAI,IAAI,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,6BAA6B,EAAE,IAC9D;QACJ,SAAS;YAAC;gBAAE,MAAM;gBAAU,KAAK;YAAsB;SAAE;QACzD,SAAS;QACT,iBAAiB;YACf,WAAW;QACb;QACA,aAAa;YACX,SAAS;YACT,gBAAgB;YAChB,OAAO,aAAa,KAAK;QAC3B;QACA,WAAW;YACT,OAAO,aAAa,KAAK;YACzB,aAAa,aAAa,WAAW;YACrC,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA,WAAW;QACX,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF;AACF;AAEA,MAAM,OAAO,OAAO,EAAE,MAAM,EAAa;IACvC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,aAAa,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IACvC,MAAM,cAAc,MAAM,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAExC,qBACE;;YACG,6BACC,6VAAC;gBAAI,WAAU;0BAA8C;;;;;;0BAK/D,6VAAC;gBAAI,WAAU;gBAA+B,OAAO;oBAAE,iBAAiB;gBAAU;0BAChF,cAAA,6VAAC;oBACC,WAAU;oBACV,OAAO;wBACL,QAAQ;wBACR,WAAW;wBACX,iBAAiB;oBACnB;;sCAGA,6VAAC;4BACC,WAAU;4BACV,OAAO;gCACL,YAAY;4BACd;;;;;;sCAKF,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC,mKAAA,CAAA,OAAI;gCAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;0BAMxB,6VAAC;gBAAI,WAAU;gBAA8B,OAAO;oBAAE,iBAAiB;gBAAU;0BAC/E,cAAA,6VAAC;oBACC,WAAU;oBACV,OAAO;wBACL,iBAAiB;wBACjB,QAAQ;wBACR,WAAW;oBACb;;sCAIE,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAG,WAAU;sDAAgD;;;;;;sDAG9D,6VAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;8CAMzD,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;sDAA0F;;;;;;sDAGzG,6VAAC;4CAAI,WAAU;sDAA0F;;;;;;sDAGzG,6VAAC;4CAAI,WAAU;sDAA0F;;;;;;;;;;;;8CAM3G,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAe,SAAQ;8DACjE,cAAA,6VAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;gDACrJ;;;;;;;sDAGR,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAe,SAAQ;8DACjE,cAAA,6VAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;gDACrJ;;;;;;;sDAGR,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAe,SAAQ;8DACjE,cAAA,6VAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;gDACrJ;;;;;;;sDAGR,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAe,SAAQ;8DACjE,cAAA,6VAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;gDACrJ;;;;;;;sDAGR,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAe,SAAQ;8DACjE,cAAA,6VAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAqH,UAAS;;;;;;;;;;;gDACrJ;;;;;;;;;;;;;;;;;;;sCAOZ,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAI,WAAU;;kDAEb,6VAAC;wCAAI,WAAU;kDACb,cAAA,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;;;;;;8DACf,6VAAC;oDAAI,WAAU;;;;;;8DACf,6VAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;kDAGnB,6VAAC,4OAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;wCACV,OAAO;4CACL,UAAU;wCACZ;wCACA,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzB,6VAAC,qKAAA,CAAA,SAAM;;;;;0BAEP,6VAAC,iLAAA,CAAA,iBAAc;;;;;0BACf,6VAAC,+KAAA,CAAA,eAAY;;;;;0BACb,6VAAC,wKAAA,CAAA,YAAS;gBAAC,YAAY;;;;;;0BACvB,6VAAC,uKAAA,CAAA,WAAQ;;;;;;;AAGf;uCAEe", "debugId": null}}]}